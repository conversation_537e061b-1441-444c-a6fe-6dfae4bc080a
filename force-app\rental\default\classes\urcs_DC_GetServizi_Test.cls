/**
 * @File Name         : urcs_DC_GetServizi_Test.cls
 * @Description       : 
 * <AUTHOR> VE
 * @Group             : 
 * @Last Modified On  : 11-07-2025
 * @Last Modified By  : VE
**/
@isTest
public class urcs_DC_GetServizi_Test {
    
    @TestSetup
    static void makeData(){
        Account testAccount = new Account();
        testAccount.Name = 'Test Account';
        insert testAccount;

        ServiceContract testServiceContract = new ServiceContract();
        testServiceContract.Name = 'Test Service Contract';
        testServiceContract.AccountId = testAccount.Id;
        testServiceContract.ExternalId__c = 'UR_12345';
        insert testServiceContract;
    }
    
    @isTest
    static void testCallMethod() {
        ServiceContract testServiceContract = [SELECT Id FROM ServiceContract WHERE Name = 'Test Service Contract' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };

        urcs_DC_GetServizi controller = new urcs_DC_GetServizi();

        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();
    }
    
    @isTest
    static void testInvokeMethod() {
        ServiceContract testServiceContract = [SELECT Id FROM ServiceContract WHERE Name = 'Test Service Contract' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetServizi.invokeMethod(input, output, options);
        Test.stopTest();
    }

    @isTest
    static void testGetServizi() {
        ServiceContract testServiceContract = [SELECT Id FROM ServiceContract WHERE Name = 'Test Service Contract' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testGetServiziWithInvalidContract() {
        String fakeContractId = '801000000000000AAA';

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => fakeContractId
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testGetServiziWithNullRecordId() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => null
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testInvokeMethodException() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'invalid',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.invokeMethod(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
}